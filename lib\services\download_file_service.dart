import 'dart:io';
import 'dart:math';

import 'package:background_downloader/background_downloader.dart';
import 'package:ddone/models/download_result_model.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:http/http.dart' as http;
import 'package:media_store_plus/media_store_plus.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:permission_handler/permission_handler.dart';

class DownloadFileService {
  MediaStore? _mediaStore;

  DownloadFileService() {
    if (isMobile) {
      _mediaStore = MediaStore();
    }
  }

  /// A generic method to download a file and return a structured result.
  Future<DownloadResult> _downloadFile(String url, String fileName, String directoryPath) async {
    final task = DownloadTask(
      url: url,
      directory: directoryPath,
      filename: fileName,
    );

    log.t('Starting download: $url -> $directoryPath${p.separator}$fileName');

    final result = await FileDownloader().download(task);

    switch (result.status) {
      case TaskStatus.complete:
        final filePath = await result.task.filePath();
        log.t('Download completed successfully: $filePath');
        return DownloadResult(DownloadResultStatus.successful, filePath: filePath);
      case TaskStatus.canceled:
        log.w('Download was canceled for: $url');
        return DownloadResult(DownloadResultStatus.canceled, message: 'Download was canceled.');
      case TaskStatus.paused:
        log.w('Download was paused for: $url');
        return DownloadResult(DownloadResultStatus.paused, message: 'Download was paused.');
      case TaskStatus.notFound:
        log.e('File not found: $url');
        return DownloadResult(DownloadResultStatus.fileNotAvailable, message: 'Media no longer available.');
      default:
        log.e('Download failed with status: ${result.status} for: $url');
        return DownloadResult(DownloadResultStatus.failed, message: 'Download failed: ${result.status}');
    }
  }

  /// Downloads file to a temporary location for MediaStore operations
  Future<DownloadResult> _downloadToTemp(String fileUrl, String fileName) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFilePath = p.join(tempDir.path, fileName);

      log.t('Downloading to temp location: $tempFilePath');

      final response = await http.get(Uri.parse(fileUrl));
      if (response.statusCode == 200) {
        final file = File(tempFilePath);
        await file.writeAsBytes(response.bodyBytes);
        log.t('File downloaded to temp: $tempFilePath');
        return DownloadResult(DownloadResultStatus.successful, filePath: tempFilePath);
      } else {
        log.e('Failed to download file. Status code: ${response.statusCode}');
        return DownloadResult(DownloadResultStatus.failed,
            message: 'Failed to download file. Status code: ${response.statusCode}');
      }
    } catch (e, st) {
      log.e('Error downloading to temp', error: e, stackTrace: st);
      return DownloadResult(DownloadResultStatus.failed, message: 'Failed to download: $e');
    }
  }

  /// High-level method to save a file to the public "Downloads" folder.
  /// Uses MediaStore for Android/iOS and traditional methods for Windows/macOS.
  Future<DownloadResult> saveFileToDownloads(String fileUrl, {String? customFileName}) async {
    if (!await _isFileAccessible(fileUrl)) {
      return DownloadResult(DownloadResultStatus.fileNotAvailable, message: 'Media no longer available or accessible.');
    }

    final fileName = _sanitizeFileName(customFileName ?? _getFileNameFromUrl(fileUrl));

    if (isMobile) {
      return await _saveFileToDownloadsMobile(fileUrl, fileName);
    } else {
      return await _saveFileToDownloadsDesktop(fileUrl, fileName);
    }
  }

  /// Mobile implementation using MediaStore (Android) or Documents directory (iOS)
  Future<DownloadResult> _saveFileToDownloadsMobile(String fileUrl, String fileName) async {
    // Crucially, only access _mediaStore if it's initialized (i.e., on Android/iOS)
    if (_mediaStore == null) {
      log.e('MediaStore not initialized for mobile platform. This should not happen.');
      return DownloadResult(DownloadResultStatus.failed, message: 'Internal error: MediaStore not available.');
    }

    try {
      if (isAndroid) {
        // Request permissions if needed
        if (!await _requestAndroidPermissions()) {
          return DownloadResult(DownloadResultStatus.failed, message: 'Storage permissions denied.');
        }

        // Check if file already exists in Downloads
        final fileExists = await _mediaStore!.isFileExist(
          fileName: fileName,
          dirType: DirType.download,
          dirName: DirName.download,
        );

        if (fileExists) {
          log.t('File already exists in Downloads: $fileName');
          // Get the file URI to return the path if possible
          final fileUri = await _mediaStore!.getFileUri(
            fileName: fileName,
            dirType: DirType.download,
            dirName: DirName.download,
          );
          final filePath =
              fileUri != null ? await _mediaStore!.getFilePathFromUri(uriString: fileUri.toString()) : null;
          return DownloadResult(DownloadResultStatus.fileAlreadyExists, filePath: filePath);
        }

        // Download to temp first
        final tempDownloadResult = await _downloadToTemp(fileUrl, fileName);
        if (tempDownloadResult.status != DownloadResultStatus.successful) {
          return tempDownloadResult;
        }

        // Save to Downloads using MediaStore
        final savedFile = await _mediaStore!.saveFile(
          tempFilePath: tempDownloadResult.filePath!,
          dirType: DirType.download,
          dirName: DirName.download,
        );
        final fileExistsAfterDownload = await _mediaStore!.isFileExist(
          fileName: fileName,
          dirType: DirType.download,
          dirName: DirName.download,
        );

        final tempFile = File(tempDownloadResult.filePath!);
        if (savedFile != null || fileExistsAfterDownload) {
          final fileUri = await _mediaStore!.getFileUri(
            fileName: fileName,
            dirType: DirType.download,
            dirName: DirName.download,
          );
          final filePath =
              fileUri != null ? await _mediaStore!.getFilePathFromUri(uriString: fileUri.toString()) : null;
          log.t('File saved to Downloads via MediaStore: $filePath');
          // Clean up temp file
          if (await tempFile.exists() && await _isFileValid(tempFile)) {
            await File(tempDownloadResult.filePath!).delete();
          }
          return DownloadResult(DownloadResultStatus.successful, filePath: filePath);
        } else {
          // Clean up temp file on failure
          if (await tempFile.exists() && await _isFileValid(tempFile)) {
            await File(tempDownloadResult.filePath!).delete();
          }
          return DownloadResult(DownloadResultStatus.failed, message: 'Failed to save file to Downloads.');
        }
      } else {
        // iOS - use Documents directory
        final documentsDir = await getApplicationDocumentsDirectory();
        final filePath = p.join(documentsDir.path, fileName);

        // Check if file already exists
        if (await File(filePath).exists()) {
          log.t('File already exists in Documents: $filePath');
          return DownloadResult(DownloadResultStatus.fileAlreadyExists, filePath: filePath);
        }

        // iOS save will auto include getApplicationDocumentsDirectory as documentDir
        return _downloadFile(fileUrl, fileName, '');
      }
    } catch (e, st) {
      log.e('Error in _saveFileToDownloadsMobile', error: e, stackTrace: st);
      return DownloadResult(DownloadResultStatus.failed, message: 'Failed to save file: $e');
    }
  }

  /// Desktop implementation for Windows/macOS
  Future<DownloadResult> _saveFileToDownloadsDesktop(String fileUrl, String fileName) async {
    try {
      final downloadsPath = await _getDesktopDownloadDirectory();
      if (downloadsPath == null) {
        return DownloadResult(DownloadResultStatus.failed, message: 'Could not determine downloads directory.');
      }

      // Ensure the downloads directory exists
      final downloadsDir = Directory(downloadsPath);
      if (!await downloadsDir.exists()) {
        try {
          await downloadsDir.create(recursive: true);
          log.t('Created downloads directory: $downloadsPath');
        } catch (e) {
          log.e('Failed to create downloads directory: $downloadsPath', error: e);
          return DownloadResult(DownloadResultStatus.failed, message: 'Could not create downloads directory.');
        }
      }

      final filePath = p.join(downloadsPath, fileName);

      // Check if file already exists
      if (await File(filePath).exists()) {
        log.t('File already exists in downloads: $filePath');
        return DownloadResult(DownloadResultStatus.fileAlreadyExists, filePath: filePath);
      }

      String dirPath = downloadsPath;
      if (isMacOS) dirPath = ''; // macOs save doesn't need to specify the dirPath.
      return _downloadFile(fileUrl, fileName, dirPath);
    } catch (e, st) {
      log.e('Error in _saveFileToDownloadsDesktop', error: e, stackTrace: st);
      return DownloadResult(DownloadResultStatus.failed, message: 'Failed to save file: $e');
    }
  }

  /// High-level method to download a file to the app's cache and then open it.
  Future<DownloadResult> downloadAndOpenFile(String fileUrl) async {
    if (!await _isFileAccessible(fileUrl)) {
      return DownloadResult(DownloadResultStatus.fileNotAvailable, message: 'Media no longer available or accessible.');
    }

    try {
      final cacheDir = await getApplicationCacheDirectory();

      // Ensure the cache directory exists
      bool cacheDirExists = await cacheDir.exists();
      if (!cacheDirExists) {
        try {
          await cacheDir.create(recursive: true);
          log.t('Created downloads directory: $cacheDir');
        } catch (e) {
          log.e('Failed to create cache directory: $cacheDir', error: e);
          return DownloadResult(DownloadResultStatus.failed, message: 'Could not create cache directory.');
        }
      }

      final fileName = _sanitizeFileName(_getFileNameFromUrl(fileUrl));
      final filePath = p.join(cacheDir.path, fileName);
      final file = File(filePath);

      // Check if file exists and is valid (not empty or corrupted)
      bool fileValid = await _isFileValid(file);
      if (fileValid) {
        log.t('File already exists in cache and is valid, opening: $filePath');
        String openFilePath = filePath;
        if (isWindows) {
          openFilePath = await _windowsCopyToPublicTemp(file);
        }
        final openResult = await OpenFilex.open(openFilePath);
        if (openResult.type == ResultType.done) {
          return DownloadResult(DownloadResultStatus.fileAlreadyExists, filePath: filePath);
        } else {
          log.w('Failed to open existing file, will re-download. Error: ${openResult.message}');
          // If file can't be opened, delete it and re-download
          await file.delete();
        }
      }

      log.t('File not in cache or invalid, downloading...');
      final downloadResult = await _downloadFile(fileUrl, fileName, cacheDir.path);

      if (downloadResult.status == DownloadResultStatus.successful && downloadResult.filePath != null) {
        String openFilePath = downloadResult.filePath!;
        if (isWindows) {
          openFilePath = await _windowsCopyToPublicTemp(File(downloadResult.filePath!));
        }
        final openResult = await OpenFilex.open(openFilePath);
        if (openResult.type != ResultType.done) {
          log.w('Downloaded file could not be opened: ${openResult.message}');
          return DownloadResult(DownloadResultStatus.failed,
              message: 'File downloaded but could not be opened: ${openResult.message}');
        }
      }

      return downloadResult;
    } catch (e, st) {
      log.e('Error in downloadAndOpenFile', error: e, stackTrace: st);
      return DownloadResult(DownloadResultStatus.failed, message: 'An unexpected error occurred: $e');
    }
  }

  /// Requests necessary permissions for Android
  Future<bool> _requestAndroidPermissions() async {
    if (_mediaStore == null || !isAndroid) {
      return false; // Permissions not applicable or MediaStore not available
    }

    try {
      final sdkInt = await _mediaStore!.getPlatformSDKInt();
      List<Permission> permissions = [];

      if (sdkInt >= 33) {
        // Android 13+ (API 33+)
        // No special permissions needed to save to Downloads on Android 13+.
        return true;
      } else if (sdkInt >= 30) {
        // Android 11-12 (API 30-32)
        permissions.add(Permission.storage);
      } else {
        // Android 10 and below (API 29-)
        permissions.addAll([
          Permission.storage,
          Permission.accessMediaLocation,
        ]);
      }

      final statuses = await permissions.request();
      final allGranted = statuses.values.every((status) => status.isGranted);

      if (!allGranted) {
        log.w('Some permissions were denied: $statuses');
      }

      return allGranted;
    } catch (e, st) {
      log.e('Error requesting Android permissions', error: e, stackTrace: st);
      return false;
    }
  }

  /// Validates if a file exists and has content
  Future<bool> _isFileValid(File file) async {
    try {
      // DO NOT use file.exists() or file.stat() as they are unreliable in MSIX.
      // Instead, try to open a read stream to the file. If it succeeds, the
      // file system virtualization is working and the file is accessible.

      // Open the file for reading.
      final f = await file.open(mode: FileMode.read);

      // Check if the file has content.
      final hasContent = await f.length() > 0;

      // Always close the file handle after you're done.
      await f.close();

      if (!hasContent) {
        log.t('File at ${file.path} is valid but empty.');
      }

      return hasContent;
    } on PathNotFoundException {
      // This is the specific, expected exception when the file does not exist at the logical path.
      // It's the sandboxed equivalent of `file.exists()` returning false.
      log.t('File not found via PathNotFoundException at: ${file.path}');
      return false;
    } catch (e) {
      // Catch any other potential I/O errors (e.g., permission denied).
      log.w('An unexpected error occurred while validating file: ${file.path}', error: e);
      return false;
    }
  }

  /// Sanitizes filename by removing invalid characters and ensuring it's not empty
  String _sanitizeFileName(String fileName) {
    if (fileName.trim().isEmpty) {
      return 'download_${DateTime.now().millisecondsSinceEpoch}';
    }

    // Remove invalid characters and limit length
    String sanitized = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');

    // Ensure filename is not too long (max 255 characters for most filesystems)
    if (sanitized.length > 255) {
      final extension = p.extension(sanitized);
      final nameWithoutExt = p.basenameWithoutExtension(sanitized);
      sanitized = '${nameWithoutExt.substring(0, 255 - extension.length)}$extension';
    }

    return sanitized;
  }

  /// Extracts filename from URL with better error handling
  String _getFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      if (pathSegments.isNotEmpty) {
        final lastSegment = pathSegments.last;
        if (lastSegment.isNotEmpty && lastSegment.contains('.')) {
          return lastSegment;
        }
      }

      // If no valid filename found, generate one with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'download_$timestamp';
    } catch (e) {
      log.w('Could not parse URL to get filename: $url', error: e);
      return 'download_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Checks if file is accessible with timeout and better error handling
  Future<bool> _isFileAccessible(String url) async {
    try {
      final response = await http.head(Uri.parse(url)).timeout(
            const Duration(seconds: 10), // Add timeout
            onTimeout: () => http.Response('', 408), // Return timeout response
          );

      final isAccessible = response.statusCode == 200;
      if (!isAccessible) {
        log.w('File not accessible. Status code: ${response.statusCode} for URL: $url');
      }
      return isAccessible;
    } catch (e) {
      log.e('Error checking file accessibility for $url', error: e);
      return false;
    }
  }

  /// Gets the appropriate download directory for desktop platforms (Windows/macOS/Linux)
  Future<String?> _getDesktopDownloadDirectory() async {
    try {
      final downloadDirectory = await getDownloadsDirectory();

      final path = downloadDirectory?.path;
      if (path != null) {
        log.t('Using desktop download directory: $path');
      } else {
        log.e('Could not determine desktop download directory');
      }

      return path;
    } catch (e) {
      log.e('Error getting desktop download directory', error: e);
      return null;
    }
  }

  /// Handles MSIX sandboxing on Windows by copying the file to a public temp location before opening.
  Future<String> _windowsCopyToPublicTemp(File file) async {
    final systemTempDir = Directory.systemTemp;

    // To avoid name collisions in the shared temp folder, create a unique name
    final uniqueFileName =
        '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(99999)}_${p.basename(file.path)}';
    final publicTempPath = p.join(systemTempDir.path, uniqueFileName);
    try {
      await file.copy(publicTempPath);
      log.t('Successfully copied file to public temp directory: $publicTempPath');
    } catch (e) {
      log.e('Failed to copy file to public temp directory.', error: e);
    }
    return publicTempPath;
  }
}
