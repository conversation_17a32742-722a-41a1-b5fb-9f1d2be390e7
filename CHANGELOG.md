# Changelog

## [2.0.13] - 2025-07-18

### Improvements
- Chat notification will now  be display after app terminiated
- Android tablet now support rotation
- Changed chat screen text selection toolbar 'copy' button text color
- Changed chat screen text highlighted color
- Auto end call when network disconnect

### Fixes
- Fixed ongoing call get ended when there is incoming call issue
- Fixed ongoing call screen button (hold, mute, speaker, etc) disappear after 2-5 min of calling issue.

## [2.0.12] - 2025-07-09

### New features
- Added last number redial functionaltiy
- Able to support external audio devices (bluetooth earphone, external speaker, etc.)

### Improvements
- Displayed loading indicator when uploading attachment
- Chat message can be be copied via long press
- Added call button in chat page
- Adeed message button in call history page 

### Fixes
- Prevented android phone from auto screen lock during call to avoid call issue
- Fixed open attachment button doesn't work in windows app issue.

## [2.0.11] - 2025-06-26

### New features
- Contact book now display your local phone book contacts

### Improvements
- Chat screen now display name instead of SIP url
- Updated iOS app icon
- Used secure websocket connection for WebRTC connection

### Fixes
- Fixed minor UI issues (removed SIP text in call button, 'cancel' text in create contact dialog now doest not get split into 2 lines)
- Windows 'open' attachment button now works properly
- Android won't display ' failed to save' after successfully saving a file
- Android 12 and below version now won't ask for photo access
- Force update prompt now can't be dismissed

## [2.0.10] - 2025-06-20

### New features
- Added 'help' button in 'Me' tab.

### Improvements
- Improved iOS callkit handling with FCM.
- Improved error prompt to be more user friendly.
- Temprarily hide group chat functionality before it is stable for testing.

### Fixes
- Fixed chat message echo back issue.
- Fixed chat unable to open attachment issue.
- Fixed chat unable to save images, video and attachment issue.
- Fixed chat notification text overflow in windows app issue.
- Fixed call time in iOS sometimes does not reset issue.
- Fixed android 13+ always ask for full intent permission issue.

## [2.0.9] - 2025-06-16

- Improved permission handling for microphone and phone on app start
- Reduced chances of occurence of duplicated SIP session. This should fix no wakeup issue and caller immediately enter voicemail issue which are both cause by duplicated SIP session.
- Call will not immediately show 'call ongoing' screen within 10s after app start and user make an outgoing call.

## [2.0.8] - 2025-06-09

- Improved permission handling when user reject camera/gallery/files permission during login
- iOS chat notification now lead user to chat page
- Added version update check

## [2.0.7] - 2025-05-30

### Fixed
- android accept call via callkit now will show calling screen
- stablized chat connection
- ios can now close keyboard in chat screen
- chat notificaiton will not show up after reading it
- recent chat now does not get reset after terminating the app

## [2.0.6] - 2025-05-21

### Fixed
- App does not show ongoing call screen after accepting call

## [2.0.5] - 2025-05-16

### Fixed
- Wake up call now work behind lock screen.

### Know Issues
- Chat connection unstable
- Group chat does not work

## [2.0.4] - 2025-04-17

### New feature
- Changed 'All' tab to 'Recent' tab in chat.

### Fixed
- Chat no longer shows 'null'.
- Chat message notification now works
- Chat now sync across devices when logged in with the same account

### Know Issues
- Chat connection unstable
- Group chat does not work

## [2.0.3] - 2025-04-04

### New feature
- NA

### Fixed
- Made accepting call and declining call more stable

### Know Issues
- NA

## [2.0.2] - 2025-03-25

### New feature
- Support on premise setup.

### Fixed
- Fixed font size inconsistent issue.
- Fixed button UI overflow in small phone issue.
- Call screen instantly crash after accepting call issue.
- Chat does not load before navigating to Contact issue.

### Know Issues
- Notification decline button unable to decline call.

## [1.4.1] - 2025-02-27
### Fixed
- Fixed edit local contact issue.

### Known Issues
- Multiple audio device problems, for Windows.
- Switch network problem, call not resume.

## [1.4.0] - 2025-02-24
### Fixed
- Fixed connection error pop up message.
- Fixed register error pop up message.

### Known Issues
- Edit local contact issue
- Multiple audio device problems, for Windows.
- Switch network problem, call not resume.

## [1.3.0] - 2024-10-28
### Fixed
- Fix the inability to end a call without an internet connection.

### Known Issues
- Multiple audio device problems, for Windows.
- Switch network problem, call not resume.

## [1.2.0] - 2024-09-19
### Added
- Dialpad sound when pressed
- Notification for call and chat
- Accept or decline call by notification
- Direct to chat by notification
- Now able to send video in chat
- Now able to search contacts in chat page
- Chat now sort by newest
- Numpad now will follow the last digit instead of user to scroll himself

### Fixed
- Fixed sometimes chat shows duplicate
- Fixed group chat not able to receive new messages
- Fixed call voice delay issue and improved voice quality
- Fixed unable to call to numbers with special characters

## [1.1.1] - 2024-08-28 (MacOS only)
### Fixed
- Fix incoming call ringtone silent issue.

## [1.1.0] - 2024-08-28
### Added
- Now able to send images in chat.
- Now able to show online group members.

### Fixed
- Fix Windows platform DTMF issue.
- Fix notification pop up every time user launch app.
- Fix duplicate message.
- Fix auto update group list after create group.
- Windows platform now can only launch app once.
- Modify fontsize for appbar.

## [1.0.0] - 2024-08-20
### Added
- First release!