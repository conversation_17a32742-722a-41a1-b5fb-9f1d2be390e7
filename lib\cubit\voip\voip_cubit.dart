import 'dart:async';

import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/events/voip_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

part 'voip_state.dart';

class VoipCubit extends Cubit<VoipState> with PrefsAware {
  late final VoipService _voipService;
  late final IEventBus _eventBus;

  RTCVideoRenderer get remoteVideoRenderer => _voipService.janusService.remoteVideoRenderer;

  VoipCubit._({
    VoipState? state,
  })  : _voipService = sl.get<VoipService>(),
        _eventBus = sl.get<IEventBus>(),
        super(state ?? const VoipInitial()) {
    _eventBus.on<VoipEvent>().listen((event) {
      log.t('voipCubit evenbus: ${event.state}');
      emitVoipEvent(event.state);
    });
    _eventBus.on<NetworkEvent>().listen((event) {
      if (event.state is NetworkReconnected) {
        log.t('VoipCubit - eventBus: networkEvent:$event');
        _voipService.maintainConnection();
      }
    });
  }

  factory VoipCubit.initial({VoipState? state}) {
    return sl.isRegistered<VoipCubit>() ? sl.get<VoipCubit>() : VoipCubit._(state: state);
  }

  Future<void> emitVoipEvent(VoipState eventBusState) async {
    if (eventBusState is VoipSipIncomingCall) {
      // 1. ignore incoming call event if already in call
      // 2. when it is hanging up, it may still need to wait for incoming call first then can only decline/hangup call
      //    so in such case incoming call is part of the hanging up process.
      if (state is VoipSipAccepted || state is VoipSipHangingUp) return;
    } else if (eventBusState is VoipSipAccepted) {
      _preventScreenOff(true);
    } else if (eventBusState is VoipSipHangup) {
      _preventScreenOff(false);
    } else if (eventBusState is VoipSipRegistered) {
      // when ttl between fusion and janus expired, janus will automatically re-register.
      // we must NOT overwrite the state when it is in call.
      if (state is VoipSipCalling ||
          state is VoipSipProceeding ||
          state is VoipSipRinging ||
          state is VoipSipAccepted ||
          state is VoipSipProgress ||
          state is VoipSipIncomingCall) return;
    }
    emit(eventBusState);
  }

  Future<void> logout() async {
    await dispose();
    // TODO: temp fix, janus listener wont trigger unregister not sure why
    // - Seems like something wrong with Janus server, janus_client package doesn't receive any response from it.
    //   Checked - Janus server received SIP REGISTER event with expried=0 from fusionPBX.
    //           - Janus server log print our 'successfully unregistered'.
    emit(const VoipSipUnregistered());
  }

  void _preventScreenOff(bool enabled) {
    try {
      if (isAndroid) {
        WakelockPlus.toggle(enable: enabled);
      }
    } catch (e) {
      log.e('failed to toggle wakelock', error: e);
    }
  }

  Future<void> dispose() async {
    await _voipService.janusService.stopStreams();
    await _voipService.janusService.disposeMedia();
    await _voipService.dispose();
  }

  @override
  Future<void> close() async {
    await dispose();
    return super.close();
  }
}
