import 'package:avatar_glow/avatar_glow.dart';
import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class OutGoingCallDialog extends StatefulWidget {
  final StopWatchTimer stopWatchTimer;

  OutGoingCallDialog({super.key}) : stopWatchTimer = sl.get<StopWatchTimer>();

  @override
  State<OutGoingCallDialog> createState() => _OutGoingCallDialogState();
}

class _OutGoingCallDialogState extends State<OutGoingCallDialog> {
  late HomeCubit _homeCubit;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VoipCubit, VoipState>(
      listener: (context, voipState) {
        if (voipState is VoipSipHangup) {
          popUntilInitial();
        } else if (voipState is VoipSipHangupError) {
          popUntilInitial();
        } else if (voipState is VoipSipAccepted) {
          // Close the outgoing call dialog since the accepted call screen
          // will be handled centrally in home.dart
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          return AlertDialog(
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(32.0))),
            insetPadding: const EdgeInsets.all(10),
            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
            content: SizedBox(
              width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
              child: BlocBuilder<VoipCubit, VoipState>(
                builder: (context, voipState) {
                  final statusMessage = voipState.statusMessage;

                  return Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      AvatarGlow(
                        glowColor: const Color.fromARGB(255, 109, 109, 109),
                        child: Material(
                          // Replace this child with your own
                          elevation: 3.0,
                          shape: const CircleBorder(),
                          child: CircleAvatar(
                            radius: context.responsiveSize<double>(
                              moileSize: 70,
                              tabletSize: 70,
                              desktopSize: context.deviceWidth(0.03),
                              largeScreenSize: context.deviceWidth(0.02),
                            ),
                            backgroundColor: const Color.fromARGB(255, 83, 83, 83),
                            child: Icon(
                              Icons.call,
                              color: Colors.orange,
                              size: context.responsiveSize<double>(
                                moileSize: 70,
                                tabletSize: 70,
                                desktopSize: context.deviceWidth(0.03),
                                largeScreenSize: context.deviceWidth(0.02),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          Text(
                            '${voipState.callee}',
                            style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                          ),
                          SizedBox(height: context.deviceHeight(0.02)),
                          Text(
                            statusMessage,
                            style: const TextStyle(color: Colors.orange),
                          ),
                          SizedBox(height: context.deviceHeight(0.03)),
                        ],
                      ),
                      SizedBox(height: context.deviceWidth(0.03)),
                      Row(
                        children: [
                          const Spacer(),
                          Padding(
                            padding: const EdgeInsets.all(0.0),
                            child: RoundShapeInkWell(
                              onTap: () {
                                popUntilInitial();
                                _homeCubit.hangup();
                              },
                              checkNetwork: false,
                              color: colorTheme.errorColor,
                              contentWidget: const Icon(Icons.call_end),
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
