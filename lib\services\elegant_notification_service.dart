import 'package:ddone/utils/string_util.dart';
import 'package:elegant_notification/elegant_notification.dart';
import 'package:elegant_notification/resources/arrays.dart';
import 'package:elegant_notification/resources/stacked_options.dart';
import 'package:flutter/material.dart';

class ElegantNotificationService {
  static const int kTitleCharLength = 40;
  static const int kDescriptionCharLength = 80;

  void showStackNotification(
    BuildContext context, {
    required String title,
    required String desc,
    GestureTapCallback? gestureTapCallback,
  }) {
    ElegantNotification.info(
      width: 360,
      stackedOptions: StackedOptions(
        key: 'topRight',
        type: StackedType.same,
        itemOffset: const Offset(0, 5),
      ),
      position: Alignment.topRight,
      animation: AnimationType.fromRight,
      title: Text(
        StringUtil.clipString(title, kTitleCharLength),
        style: const TextStyle(color: Colors.black),
      ),
      description: Text(
        StringUtil.clipString(desc, kDescriptionCharLength),
        style: const TextStyle(color: Colors.black),
      ),
      onDismiss: () {},
      onNotificationPressed: gestureTapCallback,
      animationDuration: const Duration(milliseconds: 500),
      toastDuration: const Duration(seconds: 4),
    ).show(context);
  }
}
