// lib/screens/display_image.dart

import 'package:cached_network_image/cached_network_image.dart';
import 'package:ddone/components/button/square_shape_ink_well.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/models/download_result_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/download_file_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DisplayImage extends StatefulWidget {
  final String imageUrl;
  const DisplayImage({super.key, required this.imageUrl});

  @override
  State<DisplayImage> createState() => _DisplayImageState();
}

class _DisplayImageState extends State<DisplayImage> {
  final DownloadFileService _downloadService = sl.get<DownloadFileService>();

  bool _isDownloading = false;

  Future<void> _saveImage() async {
    // Prevent multiple taps while a download is already in progress
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });
    // Show a generic loading indicator while the download starts
    EasyLoadingService().showLoadingWithText('Downloading...');

    final result = await _downloadService.saveFileToDownloads(
      widget.imageUrl,
    );

    // Always ensure the loading state is reset
    setState(() {
      _isDownloading = false;
    });

    // Handle the result directly in the UI layer
    // We dismiss the generic loader first and then show a specific result toast.
    EasyLoadingService().dismissLoading();
    switch (result.status) {
      case DownloadResultStatus.successful:
        String successText = 'Saved!';
        if (isIOS) {
          successText = 'Saved to Files!';
        } else if (isAndroid) {
          successText = 'Saved to Downloads!';
        } else if (isWindows) {
          successText = 'Saved to Downloads!';
        }
        EasyLoadingService().showSuccessWithText(successText);
        break;
      case DownloadResultStatus.failed:
      case DownloadResultStatus.fileNotAvailable:
        EasyLoadingService().showErrorWithText(result.message ?? 'Download failed');
        break;
      case DownloadResultStatus.canceled:
      case DownloadResultStatus.paused:
        EasyLoadingService().showInfoWithText(result.message ?? 'Download interrupted');
        break;
      case DownloadResultStatus.fileAlreadyExists:
        EasyLoadingService().showInfoWithText('File already exists.');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        return GestureDetector(
          onTap: pop,
          child: Scaffold(
            backgroundColor: colorTheme.backgroundColor,
            appBar: AppBar(
              backgroundColor: colorTheme.backgroundColor,
              elevation: 0,
              actions: [
                SquareShapeInkWell(
                  // Update the onTap and the button's content
                  onTap: _saveImage,
                  contentWidget: _isDownloading
                      ? Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(
                            strokeWidth: 2.0,
                            color: colorTheme.primaryColor,
                          ),
                        )
                      : const Icon(
                          Icons.download,
                          size: iconSizeLarge,
                        ),
                  color: Colors.transparent,
                ),
              ],
            ),
            body: Center(
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: context.deviceHeight(
                      0.7,
                    ),
                    minWidth: context.deviceWidth(
                      0.7,
                    ),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: widget.imageUrl,
                    fit: BoxFit.contain,
                    errorWidget: (context, url, error) {
                      return Container(
                        width: MediaQuery.sizeOf(context).width * 0.25,
                        height: 50,
                        color: Colors.grey,
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                // Placeholder icon for error
                                Icons.error,
                              ),
                              Text(
                                'Image having error',
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
