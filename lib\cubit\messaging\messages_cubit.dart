import 'dart:convert';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/message_model.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/utils/notification_util.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/moxxmpp.dart';

part 'messages_state.dart';

class MessagesCubit extends Cubit<MessagesState> with PrefsAware {
  final Logger log;
  final NotificationService notificationService;

  MessagesCubit._()
      : log = sl.get<Logger>(),
        notificationService = sl.get<NotificationService>(),
        super(MessagesState(conversation: MessagesState.conversations));

  factory MessagesCubit.initial() {
    return MessagesCubit._();
  }

  Future<void> sendMessage({
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    required ChatUiCubit chatUiCubit,
    required ContactsCubit contactsCubit,
    required String text,
    required String receiverJid,
  }) async {
    if (loginCubit.state is LoginAuthenticated) {
      emit(MessagesState(conversation: MessagesState.conversations));
      LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      if (mamListCubit.state is MamListUpdated) {
        String? sipNum = prefs.getString(CacheKeys.sipNumber);
        String? sipDomain = prefs.getString(CacheKeys.sipDomain);
        var sipAccount = '${sipNum!}@${sipDomain!}';
        final manager = currentLoginState.connection.getManagerById<MessageManager>(messageManager)!;

        if (receiverJid.contains('muc')) {
          final response = await manager.sendMessageAndWaitResponse(
            JID.fromString(receiverJid),
            TypedMap<StanzaHandlerExtension>.fromList(
              [
                MessageBodyData(
                  text,
                ),
                // const MessageIdData(),
              ],
            ),
            type: 'groupchat',
          );
          log.d('MessageCubit - sendMessage - response:${response?.toXml()}');

          mamListCubit.addMessage(
            receiverJid,
            '',
            text,
            'groupchat',
            DateTime.now().toUtc().toString(),
            '',
            sipAccount,
          );

          mamListCubit.getBookmarkList(contactsCubit: contactsCubit);
          mamListCubit.getCombinedFullList(contactsCubit: contactsCubit);
        } else {
          await manager.sendMessage(
            JID.fromString(receiverJid),
            TypedMap<StanzaHandlerExtension>.fromList(
              [
                MessageBodyData(
                  text,
                ),
              ],
            ),
            type: 'chat',
          );
          // log.d('MessageCubit - sendMessage - response:${response?.toXml()}');

          mamListCubit.addMessage(
            receiverJid,
            '',
            text,
            'chat',
            DateTime.now().toUtc().toString(),
            '',
            '',
          );
          mamListCubit.getCombinedContactList(contactsCubit: contactsCubit);
          mamListCubit.getCombinedFullList(contactsCubit: contactsCubit);
        }
      }
    }
    chatUiCubit.update();
  }

  Future<void> receiveMessage({
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    required ChatUiCubit chatUiCubit,
    required ReceiveMessageCubit receiveMessageCubit,
    required String text,
    required String senderJid,
    required String id,
    required String grpJid,
    String? groupToJoin,
    String? reasonToJoin,
  }) async {
    if (loginCubit.state is LoginAuthenticated) {
      emit(MessagesState(conversation: MessagesState.conversations));
      if (mamListCubit.state is MamListUpdated) {
        String? sipNum = prefs.getString(CacheKeys.sipNumber);
        String? sipDomain = prefs.getString(CacheKeys.sipDomain);
        // print('mamListCubit.state in MessageCubit: ${mamListCubit.state}');

        var sipAccount = '${sipNum!}@${sipDomain!}';

        final localNotificationServive = sl.get<NotificationService>();
        // final newMessage = MamInfo(
        //   '<EMAIL>',
        //   senderJid,
        //   text,
        //   'chat',
        //   DateTime.now().toUtc().toString(),
        //   '',
        // );
        if (senderJid.contains('muc')) {
          if (grpJid == sipAccount) {
            return;
          } else {
            mamListCubit.addReceiveMessage(
              '',
              senderJid,
              text,
              'groupchat',
              DateTime.now().toUtc().toString(),
              '',
              grpJid,
            );

            receiveMessageCubit.receiveMessage(
              jid: senderJid,
              name: senderJid.contains('@') ? senderJid.split('@').first : senderJid,
              message: text,
              isGroup: true,
            );

            // Show notification when user is not in the chat screen with the sender
            if ((!notificationService.isChatActive(grpJid) || !(await isAppOpen())) &&
                !notificationService.hasProcessedNotification(id)) {
              localNotificationServive.showLocalNotification(
                title: senderJid.contains('_') ? 'Group: ${senderJid.split('_').first}' : 'Group: $senderJid',
                desc: text,
                payload: json.encode({
                  'routeName': RecentChat.routeName,
                  'jid': senderJid,
                  'isGroup': true,
                }),
                // windowNotificationMessage: windowsNotificationMessage(
                //   id: 'notificationid_1',
                //   group: 'incoming_call_group',
                //   launch: 'callLaunchArg',
                //   payload: {'type': 'incoming_call'},
                // ),
                windowNotificationMessage: windowsNotificationMessage(
                  id: 'notificationid_1',
                  group: 'incoming_message_group',
                  launch: 'messageLaunchArg',
                  payload: {
                    'type': 'incoming_message',
                    'routeName': RecentChat.routeName,
                    'jid': senderJid,
                    'isGroup': true,
                  },
                ),
                windowsNotificationTemplate: singleButtonsNotificationTemplateForWindows(
                  title: senderJid.contains('@') ? senderJid.split('@').first : senderJid,
                  desc: text,
                  buttonText: 'Go to message',
                  buttonArg: 'buttonArg',
                  launch: 'messageLaunchArg',
                  scenario: 'incomingMessage',
                ),
              );
              notificationService.saveNotificationId(id);
            }
          }
        } else {
          if (grpJid == sipAccount) {
            return;
          } else {
            mamListCubit.addReceiveMessage(
              '',
              senderJid,
              text,
              'chat',
              DateTime.now().toUtc().toString(),
              '',
              '',
            );

            receiveMessageCubit.receiveMessage(
              jid: senderJid,
              name: senderJid.contains('@') ? senderJid.split('@').first : senderJid,
              message: text,
              isGroup: false,
            );

            if ((!notificationService.isChatActive(senderJid) || !(await isAppOpen())) &&
                !notificationService.hasProcessedNotification(id)) {
              localNotificationServive.showLocalNotification(
                title: senderJid.contains('@') ? senderJid.split('@').first : senderJid,
                desc: text,
                payload: json.encode({
                  'routeName': RecentChat.routeName,
                  'jid': senderJid,
                  'isGroup': false,
                }),
                // windowNotificationMessage: windowsNotificationMessage(
                //   id: 'notificationid_1',
                //   group: 'incoming_call_group',
                //   launch: 'callLaunchArg',
                //   payload: {'type': 'incoming_call'},
                // ),
                windowNotificationMessage: windowsNotificationMessage(
                  id: 'notificationid_1',
                  group: 'incoming_message_group',
                  launch: 'messageLaunchArg',
                  payload: {
                    'type': 'incoming_message',
                    'routeName': RecentChat.routeName,
                    'jid': senderJid,
                    'isGroup': false,
                  },
                ),
                windowsNotificationTemplate: singleButtonsNotificationTemplateForWindows(
                  title: senderJid.contains('@') ? senderJid.split('@').first : senderJid,
                  desc: text,
                  buttonText: 'Go to message',
                  buttonArg: 'buttonArg',
                  launch: 'messageLaunchArg',
                  scenario: 'incomingMessage',
                ),
              );
              notificationService.saveNotificationId(id);
            }
          }
        }
      }
    }
    chatUiCubit.update();
  }

  // Future<void> receiveMessage({
  //   required BuildContext receiveContext,
  //   required String text,
  //   required String receiverJid,
  //   required String session,
  //   // required bool isSender,
  //   // required String sender,
  // }) async {
  //   emit(MessagesStateNew());

  //   final contains = session
  //       .contains("$receiverJid + <EMAIL>");

  //   debugPrint(
  //       "Test the receive session : $session, $contains , $receiverJid + <EMAIL>");

  //   if (session
  //       .contains("<EMAIL> + $receiverJid")) {
  //     debugPrint('if session');
  //     addMessage(receiverJid: receiverJid, text: text);
  //   } else {
  //     debugPrint('else session $session');
  //     addReceivedMessage(
  //       // receiverJid: receiverJid,
  //       text: text,
  //       session: session,
  //     );
  //   }

  //   // if (receiveContext.read<LoginCubit>().state is LoginAuthenticated) {
  //   //   LoginAuthenticated currentLoginState =
  //   //       receiveContext.read<LoginCubit>().state as LoginAuthenticated;

  //   //   currentLoginState.connection
  //   //       .asBroadcastStream()
  //   //       .where((event) => event is MessageEvent)
  //   //       .listen((event) {
  //   //     event as MessageEvent;

  //   //     final body = event.extensions.get<MessageBodyData>()?.body;
  //   //     final from = event.from;
  //   //     final to = event.to;
  //   //     // final messagesCubit = MessagesCubit.getLastest();

  //   //     // receiveContext.read<MessagesCubit>().receiveMessage(
  //   //     //     receiveContext: receiveContext,
  //   //     //     receiverJid: to.toString(),
  //   //     //     text: body,
  //   //     //     session: '${fromSplit[0]} + ${toSplit[0]}');
  //   //   });
  //   // }
  //   receiveContext.read<ChatUiCubit>().update();
  //   debugPrint('Message receive : $text, To : $receiverJid');
  // }
}
