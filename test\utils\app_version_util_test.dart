import 'package:ddone/utils/app_version_util.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getReleaseVersion - ', () {
    test('when version found in pubspec.yaml, then should return it', () async {
      // Arrange
      Future<String> mockLoadPubSpec() async {
        return '''
name: ddone
description: An app by DotDash.
publish_to: "none"
version: 1.3.0+1
environment:
  sdk: ">=3.4.0 <=4.0.0"
        ''';
      }

      // Act
      final version = await getReleaseVersion(mockLoadPubSpec);

      // Assert
      expect(version, '1.3.0+1');
    });

    test('when version not found in pubspec.yaml, then should return "Error: Version not found"', () async {
      // Arrange
      Future<String> mockLoadPubSpec() async {
        return '''
name: ddone
description: An app by DotDash.
publish_to: "none"
environment:
  sdk: ">=3.4.0 <=4.0.0"
        ''';
      }

      // Act
      final version = await getReleaseVersion(mockLoadPubSpec);

      // Assert
      expect(version, 'Error: Version not found');
    });
  });
}
