import 'package:ddone/models/enums/platform_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';

void main() {
  group('Screen Util Tablet Detection Tests', () {
    setUp(() {
      // Reset service locator for each test
      if (GetIt.instance.isRegistered<PlatformEnum>()) {
        GetIt.instance.unregister<PlatformEnum>();
      }
    });

    testWidgets('isAndroidTablet should return true for tablet-sized Android device', (WidgetTester tester) async {
      // Register Android platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.android);

      // Create a widget with tablet-sized screen (800x600)
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Override MediaQuery to simulate tablet screen size
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(800, 600),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Test the tablet detection
                    final isTablet = isAndroidTablet(context);
                    expect(isTablet, isTrue);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('isAndroidTablet should return false for phone-sized Android device', (WidgetTester tester) async {
      // Register Android platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.android);

      // Create a widget with phone-sized screen (400x800)
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Override MediaQuery to simulate phone screen size
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(400, 800),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Test the tablet detection
                    final isTablet = isAndroidTablet(context);
                    expect(isTablet, isFalse);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('isAndroidTablet should return false for non-Android platform', (WidgetTester tester) async {
      // Register iOS platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.ios);

      // Create a widget with tablet-sized screen
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(800, 600),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Test the tablet detection - should be false for iOS
                    final isTablet = isAndroidTablet(context);
                    expect(isTablet, isFalse);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('isTablet should work with different screen sizes', (WidgetTester tester) async {
      // Register Android platform for this test
      sl.registerSingleton<PlatformEnum>(PlatformEnum.android);

      // Test cases with different screen sizes
      final testCases = [
        {'size': const Size(1024, 768), 'expected': true, 'description': 'Large tablet'},
        {'size': const Size(600, 800), 'expected': true, 'description': 'Small tablet (600dp width)'},
        {'size': const Size(800, 600), 'expected': true, 'description': 'Small tablet (600dp smallest width)'},
        {'size': const Size(500, 800), 'expected': false, 'description': 'Large phone'},
        {'size': const Size(320, 568), 'expected': false, 'description': 'Small phone'},
      ];

      for (final testCase in testCases) {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                return MediaQuery(
                  data: MediaQueryData(
                    size: testCase['size'] as Size,
                    devicePixelRatio: 2.0,
                  ),
                  child: Builder(
                    builder: (context) {
                      final result = isAndroidTablet(context);
                      expect(result, testCase['expected'],
                          reason: 'Failed for ${testCase['description']} with size ${testCase['size']}');
                      return const Scaffold(body: Text('Test'));
                    },
                  ),
                );
              },
            ),
          ),
        );
      }
    });

    tearDown(() {
      // Clean up service locator after each test
      if (GetIt.instance.isRegistered<PlatformEnum>()) {
        GetIt.instance.unregister<PlatformEnum>();
      }
    });
  });
}
