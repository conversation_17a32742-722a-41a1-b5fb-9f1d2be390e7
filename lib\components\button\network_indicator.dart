import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NetworkIndicatorButton extends StatefulWidget {
  const NetworkIndicatorButton({super.key});

  @override
  State<NetworkIndicatorButton> createState() => _NetworkIndicatorButtonState();
}

class _NetworkIndicatorButtonState extends State<NetworkIndicatorButton> with SingleTickerProviderStateMixin {
  late HomeCubit _homeCubit;
  late VoipCubit _voipCubit;
  late AuthCubit _authCubit;

  late AnimationController _animationController;
  late Animation<double> _fadeInFadeOut;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _authCubit = BlocProvider.of<AuthCubit>(context);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeInFadeOut = Tween<double>(
      begin: 0.0,
      end: 1,
    ).animate(_animationController);

    _animationController.forward();
    _animationController.repeat(reverse: true);

    // _animationController.addListener(() {
    //   if (_voipCubit.state is VoipSipRegistered && _networkCubit.state is! NetworkDisconnected) {
    //     if (_animationController.isDismissed) {
    //       _animationController.forward();
    //       _animationController.repeat(reverse: true);
    //     }
    //   } else {
    //     _animationController.reset();
    //   }
    // });
  }

  @override
  void dispose() {
    _animationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NetworkCubit, NetworkState>(builder: (context, networkState) {
      return BlocBuilder<VoipCubit, VoipState>(
        builder: (context, voipState) {
          final bool isOnlineAndRegistered = voipState is VoipSipRegistered && networkState is! NetworkDisconnected;
          final bool isDoingStuff = voipState is VoipSipRegistering || voipState is VoipSipHangingUp;
          Color indicatorColor = Colors.red;
          if (isDoingStuff) {
            indicatorColor = Colors.yellow;
          } else {
            indicatorColor = Colors.green;
          }

          return Center(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              child: Tooltip(
                message: isOnlineAndRegistered ? 'Connected' : 'Not Connected',
                child: IconButton(
                  icon: FadeTransition(
                    opacity: _fadeInFadeOut,
                    child: Icon(
                      Icons.circle,
                      color: indicatorColor,
                    ),
                  ),
                  iconSize: 28,
                  splashRadius: context.deviceHeight(0.02),
                  onPressed: () {
                    if (isOnlineAndRegistered || isDoingStuff) return;
                    log.t('Manual trigger initialize RtcClient');
                    _homeCubit.initRtcClient(
                      authCubit: _authCubit,
                    );
                  },
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
