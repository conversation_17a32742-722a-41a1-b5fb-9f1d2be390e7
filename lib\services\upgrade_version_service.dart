import 'package:janus_client/janus_client.dart';
// import 'package:upgrader/upgrader.dart';

import 'package:http/http.dart' as http;

class UpgrateVersionService {
  Future<String?> fetchMicrosoftStoreVersion(String storeUrl) async {
    try {
      final response = await http.get(Uri.parse(storeUrl));
      if (response.statusCode == 200) {
        // Parse the HTML response
        final document = parse(response.body);

        // Find the version information in the "Additional Information" section
        final versionElement = document.querySelector('div.version-info-class'); // Replace with the actual class or id
        return versionElement?.text.trim();
      }
    } catch (e) {
      print("Error fetching version: $e");
    }
    return null;
  }
//   String message(UpgraderMessage messageKey){
//     switch (messageKey) {
//       case UpgraderMessage.body:
//         return 'A new version of {{appName}} is avalaible!';
//       case UpgraderMessage.buttonTitleIgnore:

//       default:
//     }
//   }
//   Future<void> alertUpgrade() async {
//     UpgradeAlert(
//       upgrader: Upgrader(

//       ),
//     );
//     // final result =
//     // return;
//   }
}
