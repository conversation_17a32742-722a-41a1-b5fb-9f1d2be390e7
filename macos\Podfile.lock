PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/CoreOnly (11.4.2):
    - FirebaseCore (= 11.4.2)
  - Firebase/Messaging (11.4.2):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.4.0)
  - firebase_core (3.8.0):
    - Firebase/CoreOnly (~> 11.4.0)
    - FlutterMacOS
  - firebase_messaging (15.1.5):
    - Firebase/CoreOnly (~> 11.4.0)
    - Firebase/Messaging (~> 11.4.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (11.4.2):
    - FirebaseCoreInternal (< 12.0, >= 11.4.2)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.5.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.1):
    - FlutterMacOS
  - flutter_volume_controller (0.0.1):
    - FlutterMacOS
  - flutter_webrtc (0.9.36):
    - FlutterMacOS
    - WebRTC-SDK (= 114.5735.08)
  - FlutterMacOS (1.0.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - LaunchAtLogin (2.5.0)
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_native_event_loop (1.0.0):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - network_info_plus (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - Sentry/HybridSDK (8.35.1)
  - sentry_flutter (8.8.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.35.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - tray_manager (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - WebRTC-SDK (114.5735.08)
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - flutter_volume_controller (from `Flutter/ephemeral/.symlinks/plugins/flutter_volume_controller/macos`)
  - flutter_webrtc (from `Flutter/ephemeral/.symlinks/plugins/flutter_webrtc/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - LaunchAtLogin
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_native_event_loop (from `Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - network_info_plus (from `Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - sentry_flutter (from `Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - tray_manager (from `Flutter/ephemeral/.symlinks/plugins/tray_manager/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - LaunchAtLogin
    - nanopb
    - PromisesObjC
    - Sentry
    - WebRTC-SDK

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  flutter_volume_controller:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_volume_controller/macos
  flutter_webrtc:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_webrtc/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_native_event_loop:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  network_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  sentry_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  tray_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/tray_manager/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  audio_session: eaca2512cf2b39212d724f35d11f46180ad3a33e
  connectivity_plus: b21496ab28d1324eb59885d888a4d83b98531f01
  device_info_plus: b0fafc687fb901e2af612763340f1b0d4352f8e5
  file_selector_macos: 585232b688707857504f9cb5f985a7c97fe4dd30
  Firebase: 7fd5466678d964be78fbf536d8a3385da19c4828
  firebase_core: 5235a039bae1d526520162a2e5e3e57e872f0b39
  firebase_messaging: 786f2f7fd952820abe23a32eecf4ddc0f6fb692d
  FirebaseCore: 6b32c57269bd999aab34354c3923d92a6e5f3f84
  FirebaseCoreInternal: f47dd28ae7782e6a4738aad3106071a8fe0af604
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: f8a160d99c2c2e5babbbcc90c4a3e15db036aee2
  flutter_local_notifications: 7e5a17a1dbc00d83dc10d43c2c4c05f2ceed233c
  flutter_secure_storage_macos: b2d62a774c23b060f0b99d0173b0b36abb4a8632
  flutter_volume_controller: ae08bf0838e4901f299781c3ab5056dfab0c2f74
  flutter_webrtc: 44172a666f0f625297c6e8b0a431918f7426a884
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  LaunchAtLogin: 94138ff26baefa09a29f5e6679a6f4c41ca6b416
  media_kit_libs_macos_video: 85a23e549b5f480e72cae3e5634b5514bc692f65
  media_kit_native_event_loop: a80d071c835c612fd80173e79390a50ec409f1b1
  media_kit_video: fa6564e3799a0a28bff39442334817088b7ca758
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  network_info_plus: 21d1cd6a015ccb2fdff06a1fbfa88d54b4e92f61
  package_info_plus: 122abb51244f66eead59ce7c9c200d6b53111779
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  screen_brightness_macos: 2a3ee243f8051c340381e8e51bcedced8360f421
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  Sentry: 1fe34e9c2cbba1e347623610d26db121dcb569f1
  sentry_flutter: e24b397f9a61fa5bbefd8279c3b2242ca86faa90
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  tray_manager: a104b5c81b578d83f3c3d0f40a997c8b10810166
  url_launcher_macos: de10e46d8d8b9e3a7b8a133e8de92b104379f05e
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497
  WebRTC-SDK: c24d2a6c9f571f2ed42297cb8ffba9557093142b
  window_manager: 1d01fa7ac65a6e6f83b965471b1a7fdd3f06166c

PODFILE CHECKSUM: a08d8a22a27952cf074e89c3c6b3c406b15bbf1d

COCOAPODS: 1.16.2
