import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:http/http.dart' as http;
import 'package:moxxmpp/moxxmpp.dart';

part 'file_picker_state.dart';

class FilePickerCubit extends Cubit<FilePickerState> {
  FilePickerCubit._() : super(FilePickerInitial());

  factory FilePickerCubit.initial() {
    return FilePickerCubit._();
  }

  FilePickerResult? _fileResult;

  String? checkFileExtension(String extension) {
    if (extension == 'mp4' || extension == 'mkv' || extension == 'mov') {
      return 'Video';
    } else if (extension == 'jpeg' || extension == 'png' || extension == 'jpg' || extension == 'svg') {
      return 'Image';
    } else {
      return 'File';
    }
  }

  Future<void> pickFile() async {
    _fileResult = await FilePicker.platform.pickFiles(
      type: FileType.any,
      dialogTitle: 'Select a file/image to send',
      // allowedExtensions: [
      //   'doc',
      //   'docx',
      //   'pdf',
      //   'ppt',
      //   'pptx',
      //   'xls',
      //   'xlsx',
      //   'csv', //Document
      //   'py',
      //   'html',
      //   'xml',
      //   'css',
      //   'js',
      //   'json',
      //   'txt',
      //   'ini', //Text
      //   'jpg',
      //   'jpeg',
      //   'png',
      //   'gif',
      //   'bmp', //Images
      // ],
    );
    // debugPrint(file);
    if (_fileResult != null && _fileResult!.files.isNotEmpty) {
      PlatformFile fileDetail = _fileResult!.files.first;

      if (fileDetail.bytes == null && fileDetail.path != null) {
        final bytes = await File(fileDetail.path!).readAsBytes();
        debugPrint('Bytes ${File(fileDetail.path!).readAsBytes()}');
        // If not, read the file into memory
        fileDetail = PlatformFile(
          name: fileDetail.name,
          path: fileDetail.path,
          size: fileDetail.size,
          bytes: bytes,
          // Populate other fields as needed
        );
      }

      if (fileDetail.bytes == null) {
        debugPrint('File bytes are null after reading from path.');
        return;
      }
      File file = File(fileDetail.path!);
      Uint8List imageData = file.readAsBytesSync();
      img.Image? image = img.decodeImage(imageData);
      emit(FilePickerSelected(selectedFile: _fileResult!));
      // Close the dialog if no file was picked
    } else {
      pop(); // Trigger a rebuild to show the selected file
    }
  }

  Future<void> _uploadFile(String url, int fileBytes, String filePath) async {
    try {
      final file = File(filePath);
      final fileContents = await file.readAsBytes();
      final response = await http.put(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/octet-stream',
          'Content-Length': fileBytes.toString(),
        },
        // headers: {
        //   "Content-Type": "application/octet-stream",
        //   // "Authorization": "Basic YWRtaW46YWRtaW4=",
        // },
        body: fileContents,
      );

      if (response.statusCode == 200) {
        debugPrint('File uploaded successfully');
        emit(FilePickerUploaded());
      } else {
        debugPrint('File upload failed: ${response.statusCode} ${response.reasonPhrase}');
        // Don't emit error state here, let the calling method handle it
        throw Exception('File upload failed: ${response.statusCode} ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('File upload failed: $e');
      // Re-throw the exception to be handled by the calling method
      rethrow;
    }
  }

  Future<void> reqSlot(
    LoginCubit loginCubit,
    MessagesCubit messagesCubit,
    MamListCubit mamListCubit,
    ChatUiCubit chatUiCubit,
    ContactsCubit contactsCubit,
    String receiver,
  ) async {
    if (loginCubit.state is! LoginAuthenticated) {
      return; // Handle non-authenticated state
    }
    LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

    // Show loading indicator and emit uploading state
    emit(FilePickerUploading());
    EasyLoadingService().showLoadingWithText('Uploading file...');

    // late HttpFileUploadManager _httpFileUploadManager = HttpFileUploadManager();
    try {
      final upFile = currentLoginState.connection.getManagerById<HttpFileUploadManager>(httpFileUploadManager)!;
      final fileExtension = _fileResult!.files.first.extension!.toLowerCase();
      final newUploadFileName = checkFileExtension(fileExtension);
      final result = await upFile.requestUploadSlot(
        _fileResult!.files.first.name.replaceAll(_fileResult!.files.first.name,
            'DDOne_${newUploadFileName}_${DateTime.now().microsecondsSinceEpoch}.$fileExtension'),
        _fileResult!.files.first.size,
      );
      if (result.isType<HttpFileUploadSlot>()) {
        var slot = result.get<HttpFileUploadSlot>();
        debugPrint('In Request Slot : $slot');
        debugPrint('Uploading file to: ${slot.putUrl}');
        debugPrint('File name: ${_fileResult!.files.first.name}');
        debugPrint('File size: ${_fileResult!.files.first.size}');
        debugPrint('File bytes: ${_fileResult!.files.first.bytes}');
        debugPrint('File bytes: ${_fileResult!.files.first.path}');

        if (_fileResult != null && _fileResult!.files.isNotEmpty) {
          try {
            await _uploadFile(
              slot.putUrl,
              _fileResult!.files.first.size,
              _fileResult!.files.first.path ?? '',
            );

            if (state is FilePickerUploaded) {
              messagesCubit.sendMessage(
                loginCubit: loginCubit,
                mamListCubit: mamListCubit,
                chatUiCubit: chatUiCubit,
                contactsCubit: contactsCubit,
                text: slot.getUrl.toString(),
                receiverJid: receiver,
              );
              // Dismiss loading indicator on successful upload and message send
              EasyLoadingService().dismissLoading();
            }
          } catch (uploadError) {
            // Handle upload failure
            EasyLoadingService().dismissLoading();
            EasyLoadingService().showErrorWithText('Failed to upload file');
            debugPrint('Upload error: $uploadError');
            return; // Exit early on upload failure
          }
          // ignore: use_build_context_synchronously
        }

        debugPrint('Get URL : ${slot.getUrl}');
        debugPrint('Put URL : ${slot.putUrl}');
        debugPrint('Headers URL : ${slot.headers}');
      }

      if (result.isType<HttpFileUploadError>()) {
        // Dismiss loading indicator on error
        EasyLoadingService().dismissLoading();
        if (result.get() is FileTooBigError) {
          EasyLoadingService().showErrorWithText('File size exceeded 10MB');
        }
        debugPrint('Error sending image/file ${result.get() is FileTooBigError}');
      }
    } catch (e) {
      // Dismiss loading indicator on exception
      EasyLoadingService().dismissLoading();
      EasyLoadingService().showErrorWithText('Failed to upload file');
      debugPrint('Error $e');
    }
  }
}
