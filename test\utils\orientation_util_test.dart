import 'package:ddone/models/enums/platform_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/orientation_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';

void main() {
  group('Orientation Util Tests', () {
    setUp(() {
      // Reset service locator for each test
      if (GetIt.instance.isRegistered<PlatformEnum>()) {
        GetIt.instance.unregister<PlatformEnum>();
      }
    });

    testWidgets('setDeviceOrientations should allow all orientations for Android tablets', (WidgetTester tester) async {
      // Register Android platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.android);

      // Track SystemChrome calls
      final List<MethodCall> methodCalls = <MethodCall>[];
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.platform,
        (MethodCall methodCall) async {
          methodCalls.add(methodCall);
          return null;
        },
      );

      // Create a widget with tablet-sized screen (800x600)
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(800, 600),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Call the orientation setup
                    OrientationUtil.setDeviceOrientations(context);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // Verify that setPreferredOrientations was called with all orientations
      expect(methodCalls.length, greaterThan(0));
      final orientationCall = methodCalls.firstWhere(
        (call) => call.method == 'SystemChrome.setPreferredOrientations',
        orElse: () => const MethodCall('notFound'),
      );

      expect(orientationCall.method, 'SystemChrome.setPreferredOrientations');
      final orientations = List<String>.from(orientationCall.arguments as List);
      expect(orientations, contains('DeviceOrientation.portraitUp'));
      expect(orientations, contains('DeviceOrientation.portraitDown'));
      expect(orientations, contains('DeviceOrientation.landscapeLeft'));
      expect(orientations, contains('DeviceOrientation.landscapeRight'));
    });

    testWidgets('setDeviceOrientations should set portrait only for Android phones', (WidgetTester tester) async {
      // Register Android platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.android);

      // Track SystemChrome calls
      final List<MethodCall> methodCalls = <MethodCall>[];
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.platform,
        (MethodCall methodCall) async {
          methodCalls.add(methodCall);
          return null;
        },
      );

      // Create a widget with phone-sized screen (400x800)
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(400, 800),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Call the orientation setup
                    OrientationUtil.setDeviceOrientations(context);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // Verify that setPreferredOrientations was called with portrait only
      expect(methodCalls.length, greaterThan(0));
      final orientationCall = methodCalls.firstWhere(
        (call) => call.method == 'SystemChrome.setPreferredOrientations',
        orElse: () => const MethodCall('notFound'),
      );

      expect(orientationCall.method, 'SystemChrome.setPreferredOrientations');
      final orientations = List<String>.from(orientationCall.arguments as List);
      expect(orientations, contains('DeviceOrientation.portraitUp'));
      expect(orientations, hasLength(1)); // Only portrait up
    });

    testWidgets('setDeviceOrientations should not call SystemChrome for iOS devices', (WidgetTester tester) async {
      // Register iOS platform
      sl.registerSingleton<PlatformEnum>(PlatformEnum.ios);

      // Track SystemChrome calls
      final List<MethodCall> methodCalls = <MethodCall>[];
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.platform,
        (MethodCall methodCall) async {
          methodCalls.add(methodCall);
          return null;
        },
      );

      // Create a widget
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(800, 600),
                  devicePixelRatio: 2.0,
                ),
                child: Builder(
                  builder: (context) {
                    // Call the orientation setup
                    OrientationUtil.setDeviceOrientations(context);
                    return const Scaffold(body: Text('Test'));
                  },
                ),
              );
            },
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // Verify that setPreferredOrientations was NOT called for iOS
      final orientationCalls = methodCalls.where(
        (call) => call.method == 'SystemChrome.setPreferredOrientations',
      );
      expect(orientationCalls, isEmpty);
    });

    tearDown(() {
      // Clean up service locator after each test
      if (GetIt.instance.isRegistered<PlatformEnum>()) {
        GetIt.instance.unregister<PlatformEnum>();
      }

      // Reset method call handler
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.platform,
        null,
      );
    });
  });
}
