import 'dart:async';
import 'dart:ui';

import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter/widgets.dart';

/// VoIP Foreground Service that handles VoIP connection pre-warming and call management
/// This service runs in the foreground and can handle VoIP calls independently of the main app
class VoipForegroundService with PrefsAware {
  static const String _notificationChannelId = 'voip_foreground_channel';

  static bool _isInitialized = false;
  static bool _isServiceRunning = false;
  static ServiceInstance? _serviceInstance;

  /// Initialize the foreground service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    final service = FlutterBackgroundService();

    await service.configure(
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: _notificationChannelId,
        initialNotificationTitle: 'VoIP Service',
        initialNotificationContent: 'Initializing VoIP connection...',
        foregroundServiceNotificationId: 888,
        foregroundServiceTypes: [AndroidForegroundType.phoneCall],
      ),
    );

    _isInitialized = true;
    log.t('VoipForegroundService initialized');
  }

  /// Entry point for the background service
  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    DartPluginRegistrant.ensureInitialized();

    _serviceInstance = service;
    _isServiceRunning = true;

    log.t('VoIP foreground service started');

    // Listen for stop service command
    service.on('stopService').listen((event) {
      service.stopSelf();
      _isServiceRunning = false;
      _serviceInstance = null;
      log.t('VoIP foreground service stopped via command');
    });

    // Listen for incoming call data
    service.on('incomingCall').listen((event) async {
      final data = event as Map<String, dynamic>;
      final caller = data['caller'] as String;
      final callerId = data['callerId'] as String;

      await _handleIncomingCallInService(service, caller, callerId);
    });

    // Update notification periodically to keep service alive
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (!_isServiceRunning) {
        timer.cancel();
        return;
      }

      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          service.setForegroundNotificationInfo(
            title: 'VoIP Service',
            content: 'Call service is running - ${DateTime.now().toString().substring(11, 19)}',
          );
        }
      }
    });
  }

  /// iOS background handler
  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  /// Handle incoming call within the service isolate
  static Future<void> _handleIncomingCallInService(
    ServiceInstance service,
    String caller,
    String callerId,
  ) async {
    try {
      log.t('Handling incoming call in service: $caller ($callerId)');

      // Update notification
      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          service.setForegroundNotificationInfo(
            title: 'Incoming Call',
            content: 'Call from $caller',
          );
        }
      }

      // Initialize services within the service isolate
      final callkitService = GetIt.I<CallkitService>();
      final janusService = GetIt.I<JanusService>();

      // Initialize CallKit
      callkitService.init(
        acceptedCallCallback: (callDirection) async {
          if (callDirection == CallDirection.incoming) {
            log.t('Call accepted in foreground service');
            // Handle call acceptance
            await _acceptCallInService(service, janusService, caller, callerId);
          }
        },
        declinedCallCallback: (callDirection) async {
          if (callDirection == CallDirection.incoming) {
            log.t('Call declined in foreground service');
            // Handle call decline
            await _declineCallInService(service, janusService, caller, callerId);
          }
        },
        endedCallCallback: () async {
          log.t('Call ended in foreground service');
          // Stop the service when call ends
          service.invoke('stopService');
        },
        missedCallCallback: () async {
          log.t('Call missed in foreground service');
          // Stop the service when call is missed
          service.invoke('stopService');
        },
      );

      // Show incoming call
      callkitService.incomingCall(caller, callerId);

      // Pre-warm VoIP connection
      await _setupJanusInService(janusService);
    } catch (e) {
      log.e('Error handling incoming call in service', error: e);
    }
  }

  /// Accept call in service
  static Future<void> _acceptCallInService(
    ServiceInstance service,
    JanusService janusService,
    String caller,
    String callerId,
  ) async {
    try {
      log.t('Accepting call in service: $caller ($callerId)');

      // Update notification
      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          service.setForegroundNotificationInfo(
            title: 'Call In Progress',
            content: 'Connected to $caller',
          );
        }
      }

      // Handle call acceptance logic here
      // This would typically involve WebRTC connection setup
    } catch (e) {
      log.e('Error accepting call in service', error: e);
    }
  }

  /// Decline call in service
  static Future<void> _declineCallInService(
    ServiceInstance service,
    JanusService janusService,
    String caller,
    String callerId,
  ) async {
    try {
      log.t('Declining call in service: $caller ($callerId)');

      // Handle call decline logic here
      // Stop the service after declining
      service.invoke('stopService');
    } catch (e) {
      log.e('Error declining call in service', error: e);
    }
  }

  /// Setup Janus connection in service
  static Future<void> _setupJanusInService(JanusService janusService) async {
    try {
      log.t('Setting up Janus connection in service');

      // Pre-warm the VoIP connection
      // This would typically involve connecting to Janus server
      // For now, just log that we're pre-warming
    } catch (e) {
      log.e('Error setting up Janus in service', error: e);
    }
  }

  /// Start the foreground service for an incoming call
  static Future<void> startForIncomingCall({
    required String caller,
    required String callerId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isServiceRunning) {
      log.t('VoipForegroundService already running, updating call info');
      _updateCallInfo(caller, callerId);
      return;
    }

    final service = FlutterBackgroundService();

    // Start the service with call information
    await service.startService();

    // Send call information to the service
    service.invoke('incomingCall', {
      'caller': caller,
      'callerId': callerId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    _isServiceRunning = true;
    log.t('VoipForegroundService started for incoming call: $caller ($callerId)');
  }

  /// Stop the foreground service
  static Future<void> stop() async {
    if (!_isServiceRunning) return;

    final service = FlutterBackgroundService();
    service.invoke('stopService');

    _isServiceRunning = false;
    log.t('VoipForegroundService stopped');
  }

  /// Update call information
  static void _updateCallInfo(String caller, String callerId) {
    final service = FlutterBackgroundService();
    service.invoke('update_call', {
      'caller': caller,
      'callerId': callerId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }
}
